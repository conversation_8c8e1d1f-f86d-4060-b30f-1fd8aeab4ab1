#!/usr/bin/env node

/**
 * 测试 DMC 数据源 count_distinct 函数修复
 * 验证 wrapAggregate 函数是否正确处理 count_distinct
 */

const { wrapAggregate } = require('../app/extend/sql-utils')

console.log('=== DMC count_distinct 函数修复测试 ===\n')

// 测试用例1：DMC 数据源调用 count_distinct（有 element 参数）
console.log('测试1: DMC 数据源 count_distinct（有 element 参数）')
try {
  const result1 = wrapAggregate('count_distinct', 'field_id', 'dmc', 'string', 'field_name')
  console.log('输入: count_distinct, field_id, dmc, string, field_name')
  console.log('输出:', result1)
  console.log('期望: count(distinct field_id)')
  console.log('结果:', result1 === 'count(distinct field_id)' ? '✅ 通过' : '❌ 失败')
} catch (error) {
  console.log('❌ 错误:', error.message)
}

console.log('\n' + '='.repeat(50) + '\n')

// 测试用例2：DMC 数据源调用 count_distinct（无 element 参数，向后兼容）
console.log('测试2: DMC 数据源 count_distinct（无 element 参数，向后兼容）')
try {
  const result2 = wrapAggregate('count_distinct', 'field_id', 'dmc', 'string')
  console.log('输入: count_distinct, field_id, dmc, string')
  console.log('输出:', result2)
  console.log('期望: count(distinct field_id)')
  console.log('结果:', result2 === 'count(distinct field_id)' ? '✅ 通过' : '❌ 失败')
} catch (error) {
  console.log('❌ 错误:', error.message)
}

console.log('\n' + '='.repeat(50) + '\n')

// 测试用例3：其他聚合函数（有 element 参数）
console.log('测试3: 其他聚合函数（有 element 参数）')
try {
  const result3 = wrapAggregate('sum', 'field_id', 'dmc', 'number', 'field_name')
  console.log('输入: sum, field_id, dmc, number, field_name')
  console.log('输出:', result3)
  console.log('期望: sum(field_id)')
  console.log('结果:', result3 === 'sum(field_id)' ? '✅ 通过' : '❌ 失败')
} catch (error) {
  console.log('❌ 错误:', error.message)
}

console.log('\n' + '='.repeat(50) + '\n')

// 测试用例4：其他聚合函数（无 element 参数，向后兼容）
console.log('测试4: 其他聚合函数（无 element 参数，向后兼容）')
try {
  const result4 = wrapAggregate('sum', 'field_id', 'dmc', 'number')
  console.log('输入: sum, field_id, dmc, number')
  console.log('输出:', result4)
  console.log('期望: sum(field_id)')
  console.log('结果:', result4 === 'sum(field_id)' ? '✅ 通过' : '❌ 失败')
} catch (error) {
  console.log('❌ 错误:', error.message)
}

console.log('\n' + '='.repeat(50) + '\n')

// 测试用例5：PostgreSQL 类型数据库（有 element 参数）
console.log('测试5: PostgreSQL 类型数据库（有 element 参数）')
try {
  const result5 = wrapAggregate('sum', 'field_id', 'postgresql', 'number', 'field_name')
  console.log('输入: sum, field_id, postgresql, number, field_name')
  console.log('输出:', result5)
  console.log('期望: sum(field_id) as "field_name"')
  console.log('结果:', result5 === 'sum(field_id) as "field_name"' ? '✅ 通过' : '❌ 失败')
} catch (error) {
  console.log('❌ 错误:', error.message)
}

console.log('\n' + '='.repeat(50) + '\n')

// 测试用例6：PostgreSQL 类型数据库（无 element 参数，向后兼容）
console.log('测试6: PostgreSQL 类型数据库（无 element 参数，向后兼容）')
try {
  const result6 = wrapAggregate('sum', 'field_id', 'postgresql', 'number')
  console.log('输入: sum, field_id, postgresql, number')
  console.log('输出:', result6)
  console.log('期望: sum(field_id) as "sum(field_id)"')
  console.log('结果:', result6 === 'sum(field_id) as "sum(field_id)"' ? '✅ 通过' : '❌ 失败')
} catch (error) {
  console.log('❌ 错误:', error.message)
}

console.log('\n=== 测试完成 ===')
